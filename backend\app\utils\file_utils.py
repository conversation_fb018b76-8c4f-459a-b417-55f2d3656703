"""
File handling utilities for document processing.
Provides file validation, storage, and metadata extraction.
"""

import os
import hashlib
import mimetypes
import magic
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from uuid import uuid4

from fastapi import UploadFile, HTTPException
from loguru import logger

from ..config import settings
from ..models.document import DocumentType


def get_file_type(filename: str, content: bytes = None) -> DocumentType:
    """Determine document type from filename and content."""
    extension = Path(filename).suffix.lower()
    
    # Map extensions to document types
    type_mapping = {
        '.pdf': DocumentType.PDF,
        '.doc': DocumentType.WORD,
        '.docx': DocumentType.WORD,
        '.ppt': DocumentType.POWERPOINT,
        '.pptx': DocumentType.POWERPOINT,
        '.xls': DocumentType.EXCEL,
        '.xlsx': DocumentType.EXCEL,
        '.png': DocumentType.IMAGE,
        '.jpg': DocumentType.IMAGE,
        '.jpeg': DocumentType.IMAGE,
        '.tiff': DocumentType.IMAGE,
        '.tif': DocumentType.IMAGE,
        '.bmp': DocumentType.IMAGE,
        '.gif': DocumentType.IMAGE,
        '.mp4': DocumentType.VIDEO,
        '.avi': DocumentType.VIDEO,
        '.mov': DocumentType.VIDEO,
        '.mkv': DocumentType.VIDEO,
        '.wmv': DocumentType.VIDEO,
        '.mp3': DocumentType.AUDIO,
        '.wav': DocumentType.AUDIO,
        '.m4a': DocumentType.AUDIO,
        '.flac': DocumentType.AUDIO,
        '.aac': DocumentType.AUDIO,
        '.txt': DocumentType.TEXT,
        '.md': DocumentType.TEXT,
        '.csv': DocumentType.TEXT,
    }
    
    doc_type = type_mapping.get(extension, DocumentType.OTHER)
    
    # Verify with MIME type if content is available
    if content and doc_type == DocumentType.OTHER:
        try:
            mime_type = magic.from_buffer(content, mime=True)
            if mime_type.startswith('image/'):
                doc_type = DocumentType.IMAGE
            elif mime_type.startswith('video/'):
                doc_type = DocumentType.VIDEO
            elif mime_type.startswith('audio/'):
                doc_type = DocumentType.AUDIO
            elif mime_type in ['application/pdf']:
                doc_type = DocumentType.PDF
            elif mime_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                doc_type = DocumentType.WORD
            elif mime_type in ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation']:
                doc_type = DocumentType.POWERPOINT
            elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
                doc_type = DocumentType.EXCEL
            elif mime_type.startswith('text/'):
                doc_type = DocumentType.TEXT
        except Exception as e:
            logger.warning(f"Failed to detect MIME type: {e}")
    
    return doc_type


def validate_file_extension(filename: str) -> bool:
    """Validate if file extension is allowed."""
    extension = Path(filename).suffix.lower()
    return extension in settings.allowed_extensions


def validate_file_size(file_size: int) -> bool:
    """Validate if file size is within limits."""
    return file_size <= settings.max_file_size


def calculate_file_hash(content: bytes) -> str:
    """Calculate SHA-256 hash of file content."""
    return hashlib.sha256(content).hexdigest()


def get_mime_type(filename: str, content: bytes = None) -> str:
    """Get MIME type of file."""
    # Try to get MIME type from content first
    if content:
        try:
            return magic.from_buffer(content, mime=True)
        except Exception as e:
            logger.warning(f"Failed to detect MIME type from content: {e}")
    
    # Fallback to filename-based detection
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or 'application/octet-stream'


def generate_unique_filename(original_filename: str) -> str:
    """Generate unique filename while preserving extension."""
    extension = Path(original_filename).suffix
    unique_id = str(uuid4())
    return f"{unique_id}{extension}"


def get_file_info(file_path: Path) -> Dict[str, Any]:
    """Get comprehensive file information."""
    try:
        stat = file_path.stat()
        return {
            'size': stat.st_size,
            'created': stat.st_ctime,
            'modified': stat.st_mtime,
            'is_file': file_path.is_file(),
            'is_dir': file_path.is_dir(),
            'extension': file_path.suffix.lower(),
            'stem': file_path.stem,
        }
    except Exception as e:
        logger.error(f"Failed to get file info for {file_path}: {e}")
        return {}


async def save_uploaded_file(
    file: UploadFile,
    tenant_id: str,
    user_id: str,
    custom_filename: Optional[str] = None
) -> Tuple[str, str, Dict[str, Any]]:
    """
    Save uploaded file to storage.
    Returns: (file_path, filename, file_info)
    """
    try:
        # Read file content
        content = await file.read()
        
        # Validate file
        if not validate_file_extension(file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"File extension not allowed: {Path(file.filename).suffix}"
            )
        
        if not validate_file_size(len(content)):
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds limit: {len(content)} bytes"
            )
        
        # Generate filename
        if custom_filename:
            filename = custom_filename
        else:
            filename = generate_unique_filename(file.filename)
        
        # Create directory structure: uploads/tenant_id/user_id/
        upload_dir = Path(settings.upload_path) / tenant_id / user_id
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Save file
        file_path = upload_dir / filename
        with open(file_path, 'wb') as f:
            f.write(content)
        
        # Get file information
        file_info = {
            'original_filename': file.filename,
            'filename': filename,
            'file_path': str(file_path),
            'file_size': len(content),
            'content_hash': calculate_file_hash(content),
            'mime_type': get_mime_type(file.filename, content),
            'document_type': get_file_type(file.filename, content),
        }
        
        logger.info(f"File saved: {file_path} ({len(content)} bytes)")
        return str(file_path), filename, file_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to save uploaded file: {e}")
        raise HTTPException(status_code=500, detail="Failed to save file")


def delete_file(file_path: str) -> bool:
    """Delete file from storage."""
    try:
        path = Path(file_path)
        if path.exists() and path.is_file():
            path.unlink()
            logger.info(f"File deleted: {file_path}")
            return True
        else:
            logger.warning(f"File not found for deletion: {file_path}")
            return False
    except Exception as e:
        logger.error(f"Failed to delete file {file_path}: {e}")
        return False


def move_file(source_path: str, destination_path: str) -> bool:
    """Move file from source to destination."""
    try:
        source = Path(source_path)
        destination = Path(destination_path)
        
        # Create destination directory if it doesn't exist
        destination.parent.mkdir(parents=True, exist_ok=True)
        
        # Move file
        source.rename(destination)
        logger.info(f"File moved: {source_path} -> {destination_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to move file {source_path} -> {destination_path}: {e}")
        return False


def copy_file(source_path: str, destination_path: str) -> bool:
    """Copy file from source to destination."""
    try:
        import shutil
        
        source = Path(source_path)
        destination = Path(destination_path)
        
        # Create destination directory if it doesn't exist
        destination.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy file
        shutil.copy2(source, destination)
        logger.info(f"File copied: {source_path} -> {destination_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to copy file {source_path} -> {destination_path}: {e}")
        return False


def cleanup_empty_directories(base_path: str) -> int:
    """Remove empty directories recursively."""
    try:
        count = 0
        base = Path(base_path)
        
        for path in sorted(base.rglob('*'), reverse=True):
            if path.is_dir() and not any(path.iterdir()):
                path.rmdir()
                count += 1
                logger.debug(f"Removed empty directory: {path}")
        
        logger.info(f"Cleaned up {count} empty directories")
        return count
        
    except Exception as e:
        logger.error(f"Failed to cleanup empty directories: {e}")
        return 0


def get_storage_usage(tenant_id: Optional[str] = None) -> Dict[str, Any]:
    """Get storage usage statistics."""
    try:
        base_path = Path(settings.upload_path)
        
        if tenant_id:
            base_path = base_path / tenant_id
        
        if not base_path.exists():
            return {'total_size': 0, 'file_count': 0, 'directory_count': 0}
        
        total_size = 0
        file_count = 0
        directory_count = 0
        
        for path in base_path.rglob('*'):
            if path.is_file():
                total_size += path.stat().st_size
                file_count += 1
            elif path.is_dir():
                directory_count += 1
        
        return {
            'total_size': total_size,
            'file_count': file_count,
            'directory_count': directory_count,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'total_size_gb': round(total_size / (1024 * 1024 * 1024), 2),
        }
        
    except Exception as e:
        logger.error(f"Failed to get storage usage: {e}")
        return {'error': str(e)}
