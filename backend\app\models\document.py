"""
Document model and related entities for IntelliVault.
Handles document storage, metadata, and processing status.
"""

import enum
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from uuid import uuid4

from sqlalchemy import Column, String, Boolean, DateTime, Enum, Text, ForeignKey, Integer, Float, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base


class DocumentType(enum.Enum):
    """Document type enumeration."""
    PDF = "pdf"
    WORD = "word"
    POWERPOINT = "powerpoint"
    EXCEL = "excel"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"
    OTHER = "other"


class DocumentStatus(enum.Enum):
    """Document processing status enumeration."""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    ARCHIVED = "archived"


class Document(Base):
    """Document model for storing file information and metadata."""
    
    __tablename__ = "documents"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Basic information
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    
    # Document classification
    document_type = Column(Enum(DocumentType), nullable=False, index=True)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.UPLOADED, nullable=False, index=True)
    
    # Content information
    title = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    content_hash = Column(String(64), nullable=True, index=True)  # SHA-256 hash
    
    # Processing information
    extracted_text = Column(Text, nullable=True)
    page_count = Column(Integer, nullable=True)
    duration = Column(Float, nullable=True)  # For audio/video files
    
    # Multi-tenancy and ownership
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    tenant_id = Column(String(100), nullable=False, index=True)
    department = Column(String(100), nullable=True, index=True)
    
    # Access control
    is_public = Column(Boolean, default=False, nullable=False)
    access_level = Column(String(20), default="private", nullable=False)  # private, department, public
    
    # Categorization and tagging
    category = Column(String(100), nullable=True, index=True)
    tags = Column(JSON, nullable=True)  # List of tags
    auto_tags = Column(JSON, nullable=True)  # Auto-generated tags
    
    # Version control
    version = Column(Integer, default=1, nullable=False)
    parent_document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Processing metadata
    processing_metadata = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Relationships
    owner = relationship("User", back_populates="documents")
    embeddings = relationship("Embedding", back_populates="document", cascade="all, delete-orphan")
    metadata_entries = relationship("DocumentMetadata", back_populates="document", cascade="all, delete-orphan")
    child_documents = relationship("Document", backref="parent_document", remote_side=[id])
    
    def __repr__(self) -> str:
        return f"<Document(id={self.id}, filename={self.filename}, status={self.status.value})>"
    
    def to_dict(self) -> dict:
        """Convert document to dictionary representation."""
        return {
            "id": str(self.id),
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_size": self.file_size,
            "mime_type": self.mime_type,
            "document_type": self.document_type.value,
            "status": self.status.value,
            "title": self.title,
            "description": self.description,
            "content_hash": self.content_hash,
            "page_count": self.page_count,
            "duration": self.duration,
            "owner_id": str(self.owner_id),
            "tenant_id": self.tenant_id,
            "department": self.department,
            "is_public": self.is_public,
            "access_level": self.access_level,
            "category": self.category,
            "tags": self.tags,
            "auto_tags": self.auto_tags,
            "version": self.version,
            "parent_document_id": str(self.parent_document_id) if self.parent_document_id else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "processing_metadata": self.processing_metadata,
            "error_message": self.error_message,
        }
    
    def is_processed(self) -> bool:
        """Check if document is successfully processed."""
        return self.status == DocumentStatus.PROCESSED
    
    def is_processing(self) -> bool:
        """Check if document is currently being processed."""
        return self.status == DocumentStatus.PROCESSING
    
    def has_failed(self) -> bool:
        """Check if document processing has failed."""
        return self.status == DocumentStatus.FAILED
    
    def can_be_accessed_by(self, user) -> bool:
        """Check if document can be accessed by user."""
        # Owner can always access
        if self.owner_id == user.id:
            return True
        
        # Admin can access all documents
        if user.role.value == "admin":
            return True
        
        # Public documents can be accessed by anyone
        if self.is_public:
            return True
        
        # Department level access
        if self.access_level == "department" and self.department == user.department:
            return True
        
        # Tenant level access for managers
        if user.role.value == "manager" and self.tenant_id == user.tenant_id:
            return True
        
        return False
    
    def get_file_extension(self) -> str:
        """Get file extension from filename."""
        return self.filename.split('.')[-1].lower() if '.' in self.filename else ''
    
    def is_multimodal(self) -> bool:
        """Check if document is multimodal (contains multiple content types)."""
        return self.document_type in [DocumentType.PDF, DocumentType.POWERPOINT, DocumentType.VIDEO]


class DocumentMetadata(Base):
    """Additional metadata for documents."""
    
    __tablename__ = "document_metadata"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Foreign key to document
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False, index=True)
    
    # Metadata key-value pairs
    key = Column(String(100), nullable=False, index=True)
    value = Column(Text, nullable=True)
    value_type = Column(String(20), default="string", nullable=False)  # string, number, boolean, json
    
    # Metadata source
    source = Column(String(50), nullable=False)  # user, system, ai, ocr, etc.
    confidence = Column(Float, nullable=True)  # Confidence score for AI-generated metadata
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    document = relationship("Document", back_populates="metadata_entries")
    
    def __repr__(self) -> str:
        return f"<DocumentMetadata(id={self.id}, document_id={self.document_id}, key={self.key})>"
    
    def to_dict(self) -> dict:
        """Convert metadata to dictionary representation."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "key": self.key,
            "value": self.value,
            "value_type": self.value_type,
            "source": self.source,
            "confidence": self.confidence,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def get_typed_value(self) -> Any:
        """Get value with appropriate type conversion."""
        if self.value is None:
            return None
        
        if self.value_type == "number":
            try:
                return float(self.value) if '.' in self.value else int(self.value)
            except ValueError:
                return self.value
        elif self.value_type == "boolean":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.value_type == "json":
            try:
                import json
                return json.loads(self.value)
            except json.JSONDecodeError:
                return self.value
        else:
            return self.value
