"""
Embedding model for storing vector representations of documents.
Handles text, image, and audio embeddings for semantic search.
"""

import enum
from datetime import datetime, timezone
from typing import List, Optional
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Enum, Text, ForeignKey, Integer, Float, JSON, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base


class EmbeddingType(enum.Enum):
    """Embedding type enumeration."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    MULTIMODAL = "multimodal"


class Embedding(Base):
    """Embedding model for storing vector representations."""
    
    __tablename__ = "embeddings"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Foreign key to document
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False, index=True)
    
    # Embedding information
    embedding_type = Column(Enum(EmbeddingType), nullable=False, index=True)
    model_name = Column(String(100), nullable=False)
    model_version = Column(String(20), nullable=True)
    
    # Vector data (stored as JSON array for compatibility)
    vector = Column(JSON, nullable=False)  # List of floats
    vector_dimension = Column(Integer, nullable=False)
    
    # Content information
    content_chunk = Column(Text, nullable=True)  # Original text chunk
    chunk_index = Column(Integer, nullable=True)  # Position in document
    chunk_size = Column(Integer, nullable=True)  # Size of chunk in characters
    
    # Metadata
    metadata = Column(JSON, nullable=True)  # Additional metadata
    confidence_score = Column(Float, nullable=True)
    
    # Processing information
    processing_time = Column(Float, nullable=True)  # Time taken to generate embedding
    
    # Multi-tenancy
    tenant_id = Column(String(100), nullable=False, index=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    document = relationship("Document", back_populates="embeddings")
    
    def __repr__(self) -> str:
        return f"<Embedding(id={self.id}, document_id={self.document_id}, type={self.embedding_type.value})>"
    
    def to_dict(self) -> dict:
        """Convert embedding to dictionary representation."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "embedding_type": self.embedding_type.value,
            "model_name": self.model_name,
            "model_version": self.model_version,
            "vector_dimension": self.vector_dimension,
            "content_chunk": self.content_chunk,
            "chunk_index": self.chunk_index,
            "chunk_size": self.chunk_size,
            "metadata": self.metadata,
            "confidence_score": self.confidence_score,
            "processing_time": self.processing_time,
            "tenant_id": self.tenant_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def get_vector(self) -> List[float]:
        """Get vector as list of floats."""
        if isinstance(self.vector, list):
            return self.vector
        return []
    
    def set_vector(self, vector: List[float]) -> None:
        """Set vector from list of floats."""
        self.vector = vector
        self.vector_dimension = len(vector)
    
    def calculate_similarity(self, other_vector: List[float]) -> float:
        """Calculate cosine similarity with another vector."""
        if not self.vector or not other_vector:
            return 0.0
        
        if len(self.vector) != len(other_vector):
            return 0.0
        
        # Cosine similarity calculation
        dot_product = sum(a * b for a, b in zip(self.vector, other_vector))
        magnitude_a = sum(a * a for a in self.vector) ** 0.5
        magnitude_b = sum(b * b for b in other_vector) ** 0.5
        
        if magnitude_a == 0 or magnitude_b == 0:
            return 0.0
        
        return dot_product / (magnitude_a * magnitude_b)
    
    def is_text_embedding(self) -> bool:
        """Check if this is a text embedding."""
        return self.embedding_type == EmbeddingType.TEXT
    
    def is_image_embedding(self) -> bool:
        """Check if this is an image embedding."""
        return self.embedding_type == EmbeddingType.IMAGE
    
    def is_audio_embedding(self) -> bool:
        """Check if this is an audio embedding."""
        return self.embedding_type == EmbeddingType.AUDIO
    
    def is_multimodal_embedding(self) -> bool:
        """Check if this is a multimodal embedding."""
        return self.embedding_type == EmbeddingType.MULTIMODAL


class EmbeddingModel(Base):
    """Model information for embeddings."""
    
    __tablename__ = "embedding_models"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Model information
    name = Column(String(100), unique=True, nullable=False, index=True)
    version = Column(String(20), nullable=False)
    provider = Column(String(50), nullable=False)  # huggingface, openai, custom, etc.
    
    # Model specifications
    embedding_type = Column(Enum(EmbeddingType), nullable=False)
    vector_dimension = Column(Integer, nullable=False)
    max_input_length = Column(Integer, nullable=True)
    
    # Model metadata
    description = Column(Text, nullable=True)
    model_url = Column(String(500), nullable=True)
    paper_url = Column(String(500), nullable=True)
    
    # Performance metrics
    accuracy_score = Column(Float, nullable=True)
    speed_score = Column(Float, nullable=True)  # Embeddings per second
    
    # Configuration
    model_config = Column(JSON, nullable=True)  # Model-specific configuration
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self) -> str:
        return f"<EmbeddingModel(id={self.id}, name={self.name}, type={self.embedding_type.value})>"
    
    def to_dict(self) -> dict:
        """Convert model to dictionary representation."""
        return {
            "id": str(self.id),
            "name": self.name,
            "version": self.version,
            "provider": self.provider,
            "embedding_type": self.embedding_type.value,
            "vector_dimension": self.vector_dimension,
            "max_input_length": self.max_input_length,
            "description": self.description,
            "model_url": self.model_url,
            "paper_url": self.paper_url,
            "accuracy_score": self.accuracy_score,
            "speed_score": self.speed_score,
            "model_config": self.model_config,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class EmbeddingCache(Base):
    """Cache for frequently accessed embeddings."""
    
    __tablename__ = "embedding_cache"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Cache key (hash of content + model)
    cache_key = Column(String(64), unique=True, nullable=False, index=True)
    
    # Cached data
    vector = Column(JSON, nullable=False)
    vector_dimension = Column(Integer, nullable=False)
    model_name = Column(String(100), nullable=False)
    
    # Cache metadata
    content_hash = Column(String(64), nullable=False, index=True)
    hit_count = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    last_accessed = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self) -> str:
        return f"<EmbeddingCache(id={self.id}, cache_key={self.cache_key}, hits={self.hit_count})>"
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    def increment_hit_count(self) -> None:
        """Increment hit count and update last accessed time."""
        self.hit_count += 1
        self.last_accessed = datetime.now(timezone.utc)
