"""
Logging configuration for IntelliVault application.
Provides structured logging with different formats and outputs.
"""

import sys
import json
from pathlib import Path
from typing import Dict, Any

from loguru import logger
from ..config import settings


def json_formatter(record: Dict[str, Any]) -> str:
    """Format log record as JSO<PERSON>."""
    log_entry = {
        "timestamp": record["time"].isoformat(),
        "level": record["level"].name,
        "logger": record["name"],
        "message": record["message"],
        "module": record["module"],
        "function": record["function"],
        "line": record["line"],
    }
    
    # Add extra fields if present
    if "extra" in record:
        log_entry.update(record["extra"])
    
    # Add exception info if present
    if record["exception"]:
        log_entry["exception"] = {
            "type": record["exception"].type.__name__,
            "value": str(record["exception"].value),
            "traceback": record["exception"].traceback,
        }
    
    return json.dumps(log_entry)


def setup_logging():
    """Setup application logging configuration."""
    # Remove default logger
    logger.remove()
    
    # Console logging
    if settings.log_format == "json":
        logger.add(
            sys.stdout,
            format=json_formatter,
            level=settings.log_level,
            colorize=False,
            serialize=False,
        )
    else:
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=settings.log_level,
            colorize=True,
        )
    
    # File logging
    if settings.log_file:
        log_path = Path(settings.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            settings.log_file,
            format=json_formatter if settings.log_format == "json" else "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=settings.log_level,
            rotation="100 MB",
            retention="30 days",
            compression="gz",
            serialize=settings.log_format == "json",
        )
    
    # Set third-party loggers to WARNING level
    import logging
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    logger.info(f"Logging configured - Level: {settings.log_level}, Format: {settings.log_format}")


def get_logger(name: str):
    """Get a logger instance with the given name."""
    return logger.bind(logger_name=name)
