"""
Database configuration and session management for IntelliVault.
Handles SQLAlchemy setup, connection pooling, and database operations.
"""

import asyncio
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from loguru import logger

from .config import settings


# Database metadata and base model
metadata = MetaData()
Base = declarative_base(metadata=metadata)

# Database engines
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


def get_database_url(async_mode: bool = False) -> str:
    """Get database URL with appropriate driver for sync/async mode."""
    url = settings.database_url
    
    if async_mode:
        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
        elif url.startswith("sqlite:///"):
            url = url.replace("sqlite:///", "sqlite+aiosqlite:///", 1)
    else:
        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+psycopg2://", 1)
    
    return url


def create_database_engine():
    """Create synchronous database engine."""
    global engine, SessionLocal
    
    database_url = get_database_url(async_mode=False)
    
    engine_kwargs = {
        "echo": settings.database_echo,
        "pool_pre_ping": True,
    }
    
    # SQLite specific configuration
    if database_url.startswith("sqlite"):
        engine_kwargs.update({
            "poolclass": StaticPool,
            "connect_args": {
                "check_same_thread": False,
                "timeout": 20,
            }
        })
    else:
        # PostgreSQL specific configuration
        engine_kwargs.update({
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30,
            "pool_recycle": 3600,
        })
    
    engine = create_engine(database_url, **engine_kwargs)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    logger.info(f"Created synchronous database engine: {database_url}")
    return engine


def create_async_database_engine():
    """Create asynchronous database engine."""
    global async_engine, AsyncSessionLocal
    
    database_url = get_database_url(async_mode=True)
    
    engine_kwargs = {
        "echo": settings.database_echo,
        "pool_pre_ping": True,
    }
    
    # SQLite specific configuration
    if database_url.startswith("sqlite"):
        engine_kwargs.update({
            "poolclass": StaticPool,
            "connect_args": {
                "check_same_thread": False,
                "timeout": 20,
            }
        })
    else:
        # PostgreSQL specific configuration
        engine_kwargs.update({
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30,
            "pool_recycle": 3600,
        })
    
    async_engine = create_async_engine(database_url, **engine_kwargs)
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )
    
    logger.info(f"Created asynchronous database engine: {database_url}")
    return async_engine


def get_db() -> Session:
    """Dependency to get synchronous database session."""
    if SessionLocal is None:
        create_database_engine()
    
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get asynchronous database session."""
    if AsyncSessionLocal is None:
        create_async_database_engine()
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Async database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Context manager for async database sessions."""
    if AsyncSessionLocal is None:
        create_async_database_engine()
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            logger.error(f"Async session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_database():
    """Initialize database tables."""
    try:
        if async_engine is None:
            create_async_database_engine()
        
        async with async_engine.begin() as conn:
            # Import all models to ensure they're registered
            from .models import user, document, embedding, audit
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
            
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_database():
    """Close database connections."""
    global engine, async_engine
    
    try:
        if async_engine:
            await async_engine.dispose()
            logger.info("Closed async database engine")
        
        if engine:
            engine.dispose()
            logger.info("Closed sync database engine")
            
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")


def create_tables():
    """Create database tables synchronously."""
    if engine is None:
        create_database_engine()
    
    try:
        # Import all models to ensure they're registered
        from .models import user, document, embedding, audit
        
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully (sync)")
        
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise


# Database event listeners
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set SQLite pragmas for better performance."""
    if "sqlite" in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=10000")
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()


class DatabaseManager:
    """Database manager for handling connections and operations."""
    
    def __init__(self):
        self.engine = None
        self.async_engine = None
        self.session_local = None
        self.async_session_local = None
    
    async def initialize(self):
        """Initialize database manager."""
        self.engine = create_database_engine()
        self.async_engine = create_async_database_engine()
        await init_database()
    
    async def close(self):
        """Close database connections."""
        await close_database()
    
    def get_session(self) -> Session:
        """Get synchronous database session."""
        return next(get_db())
    
    async def get_async_session(self) -> AsyncSession:
        """Get asynchronous database session."""
        async for session in get_async_db():
            return session


# Global database manager instance
db_manager = DatabaseManager()
