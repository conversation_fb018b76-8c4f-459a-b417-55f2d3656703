# IntelliVault - Corporate Knowledge Mining Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

## 🚀 Overview

IntelliVault is a comprehensive multimodal RAG (Retrieval-Augmented Generation) system designed for enterprise knowledge mining and intelligent document search. It processes diverse content types including documents, images, videos, and audio files to create a unified, searchable knowledge base with advanced AI-powered insights.

## ✨ Key Features

### 🔍 Multimodal Document Processing
- **Documents**: PDF, Word, PowerPoint, Excel
- **Images**: PNG, JPG, TIFF with OCR capabilities
- **Video**: MP4, AVI, MOV with automatic transcription
- **Audio**: MP3, WAV, M4A with speech-to-text

### 🧠 Advanced AI Capabilities
- **Multimodal Embeddings**: CLIP for images, sentence-transformers for text, Whisper for audio
- **Vector Search**: Powered by Chroma vector database
- **Content Synthesis**: AI-generated summaries with source citations
- **Auto-tagging**: Intelligent content categorization

### 🏢 Enterprise-Ready Features
- **Role-Based Access Control**: Admin, Manager, User roles
- **Multi-tenant Support**: Department-level isolation
- **Content Versioning**: Complete audit trail
- **On-premises Deployment**: Docker containerization
- **RESTful API**: JWT-based authentication

### 📊 Analytics & Management
- **Usage Dashboard**: Search analytics and insights
- **Admin Panel**: Content and user management
- **Responsive Design**: Desktop and mobile support

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        WEB[Web Interface]
        ADMIN[Admin Panel]
        API_DOC[API Documentation]
    end
    
    subgraph "API Gateway"
        FASTAPI[FastAPI Server]
        AUTH[JWT Authentication]
        RBAC[Role-Based Access Control]
    end
    
    subgraph "Processing Pipeline"
        DOC_PROC[Document Processor]
        OCR[OCR Engine]
        WHISPER[Whisper Transcription]
        EMBED[Embedding Generator]
    end
    
    subgraph "Storage Layer"
        VECTOR_DB[(Chroma Vector DB)]
        META_DB[(PostgreSQL)]
        FILE_STORE[File Storage]
    end
    
    subgraph "AI Models"
        CLIP[CLIP Model]
        SENTENCE[Sentence Transformers]
        WHISPER_MODEL[Whisper Model]
    end
    
    WEB --> FASTAPI
    ADMIN --> FASTAPI
    FASTAPI --> AUTH
    AUTH --> RBAC
    FASTAPI --> DOC_PROC
    DOC_PROC --> OCR
    DOC_PROC --> WHISPER
    DOC_PROC --> EMBED
    EMBED --> CLIP
    EMBED --> SENTENCE
    EMBED --> WHISPER_MODEL
    EMBED --> VECTOR_DB
    FASTAPI --> META_DB
    DOC_PROC --> FILE_STORE
```

## 🔄 Workflow Diagram

```mermaid
flowchart LR
    START([Document Upload]) --> DETECT{File Type Detection}
    
    DETECT -->|PDF/Word/PPT| TEXT_EXTRACT[Text Extraction]
    DETECT -->|Images| OCR_PROCESS[OCR Processing]
    DETECT -->|Video/Audio| TRANSCRIBE[Whisper Transcription]
    DETECT -->|Excel| STRUCT_PARSE[Structured Data Parsing]
    
    TEXT_EXTRACT --> EMBED_TEXT[Text Embedding]
    OCR_PROCESS --> EMBED_TEXT
    TRANSCRIBE --> EMBED_TEXT
    STRUCT_PARSE --> EMBED_TEXT
    
    DETECT -->|Images| EMBED_IMG[Image Embedding]
    DETECT -->|Audio| EMBED_AUDIO[Audio Embedding]
    
    EMBED_TEXT --> VECTOR_STORE[(Vector Database)]
    EMBED_IMG --> VECTOR_STORE
    EMBED_AUDIO --> VECTOR_STORE
    
    VECTOR_STORE --> SEARCH[Semantic Search]
    SEARCH --> RESULTS[Ranked Results]
    RESULTS --> SYNTHESIS[AI Synthesis]
    SYNTHESIS --> RESPONSE([Final Response])
```

## 📁 Project Structure

```
IntelliVault/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py                 # FastAPI application entry point
│   │   ├── config.py               # Configuration management
│   │   ├── database.py             # Database connection and setup
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── user.py             # User model
│   │   │   ├── document.py         # Document model
│   │   │   ├── embedding.py        # Embedding model
│   │   │   └── audit.py            # Audit trail model
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py             # Authentication endpoints
│   │   │   ├── documents.py        # Document management endpoints
│   │   │   ├── search.py           # Search endpoints
│   │   │   ├── admin.py            # Admin endpoints
│   │   │   └── analytics.py        # Analytics endpoints
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── document_processor.py  # Document processing service
│   │   │   ├── embedding_service.py   # Embedding generation service
│   │   │   ├── vector_service.py      # Vector database service
│   │   │   ├── ocr_service.py         # OCR processing service
│   │   │   ├── transcription_service.py # Audio/video transcription
│   │   │   └── search_service.py      # Search and retrieval service
│   │   ├── utils/
│   │   │   ├── __init__.py
│   │   │   ├── security.py         # Security utilities
│   │   │   ├── file_utils.py       # File handling utilities
│   │   │   ├── logging_config.py   # Logging configuration
│   │   │   └── validators.py       # Input validation utilities
│   │   └── tests/
│   │       ├── __init__.py
│   │       ├── test_auth.py
│   │       ├── test_documents.py
│   │       ├── test_search.py
│   │       └── test_services.py
│   ├── requirements.txt            # Python dependencies
│   ├── Dockerfile                  # Backend Docker configuration
│   └── .env.example               # Environment variables template
├── frontend/
│   ├── static/
│   │   ├── css/
│   │   │   ├── main.css
│   │   │   └── admin.css
│   │   ├── js/
│   │   │   ├── main.js
│   │   │   ├── search.js
│   │   │   └── admin.js
│   │   └── images/
│   ├── templates/
│   │   ├── base.html
│   │   ├── index.html
│   │   ├── search.html
│   │   ├── admin.html
│   │   └── login.html
│   └── app.py                      # Frontend Flask application
├── docker-compose.yml              # Multi-container Docker setup
├── docker-compose.dev.yml          # Development environment
├── docker-compose.prod.yml         # Production environment
├── setup.sh                        # Installation script
├── .env.example                    # Environment variables template
├── .gitignore                      # Git ignore rules
├── LICENSE                         # MIT License
└── docs/
    ├── API.md                      # API documentation
    ├── DEPLOYMENT.md               # Deployment guide
    ├── CONTRIBUTING.md             # Contributing guidelines
    └── ARCHITECTURE.md             # Detailed architecture docs
```

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Docker & Docker Compose
- 8GB+ RAM recommended
- 10GB+ disk space

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/IntelliVault.git
cd IntelliVault
```

2. **Run setup script**
```bash
chmod +x setup.sh
./setup.sh
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start with Docker Compose**
```bash
docker-compose up -d
```

5. **Access the application**
- Web Interface: http://localhost:8080
- API Documentation: http://localhost:8000/docs
- Admin Panel: http://localhost:8080/admin

### Development Setup

```bash
# Backend development
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Frontend development
cd frontend
python app.py
```

## 📚 API Documentation

### Authentication
```bash
# Login
POST /api/auth/login
{
  "username": "admin",
  "password": "password"
}

# Response
{
  "access_token": "jwt_token_here",
  "token_type": "bearer"
}
```

### Document Upload
```bash
# Upload document
POST /api/documents/upload
Headers: Authorization: Bearer <token>
Content-Type: multipart/form-data

# Response
{
  "document_id": "uuid",
  "filename": "document.pdf",
  "status": "processing"
}
```

### Search
```bash
# Semantic search
POST /api/search
Headers: Authorization: Bearer <token>
{
  "query": "machine learning algorithms",
  "limit": 10,
  "filters": {
    "department": "engineering",
    "file_type": "pdf"
  }
}
```

## 🔧 Configuration

Key environment variables:

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/intellivault
VECTOR_DB_PATH=./data/chroma

# Authentication
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Models
OPENAI_API_KEY=your-openai-key  # Optional for enhanced features
HUGGINGFACE_API_KEY=your-hf-key  # Optional

# Storage
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=100MB

# OCR
GOOGLE_VISION_API_KEY=your-google-vision-key  # Optional
```

## 🧪 Testing

```bash
# Run all tests
cd backend
python -m pytest

# Run with coverage
python -m pytest --cov=app tests/

# Run specific test
python -m pytest tests/test_search.py -v
```

## 🚀 Deployment

### Production Deployment
```bash
# Production with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

See [CONTRIBUTING.md](docs/CONTRIBUTING.md) for detailed guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [IntelliVault Community](https://discord.gg/intellivault)
- 📖 Documentation: [docs.intellivault.com](https://docs.intellivault.com)
- 🐛 Issues: [GitHub Issues](https://github.com/HectorTa1989/IntelliVault/issues)

## 🙏 Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) for the excellent web framework
- [Chroma](https://www.trychroma.com/) for vector database capabilities
- [OpenAI Whisper](https://openai.com/research/whisper) for transcription
- [CLIP](https://openai.com/research/clip) for multimodal embeddings
- [Sentence Transformers](https://www.sbert.net/) for text embeddings

---

**Built with ❤️ for enterprise knowledge management**
