"""
Input validation utilities for IntelliVault application.
Provides validation functions for various data types and formats.
"""

import re
import html
import unicodedata
from typing import Optional, List, Dict, Any
from email_validator import validate_email as email_validate, EmailNotValidError
from loguru import logger


def validate_email(email: str) -> bool:
    """Validate email address format."""
    try:
        email_validate(email)
        return True
    except EmailNotValidError:
        return False


def validate_password(password: str) -> Dict[str, Any]:
    """
    Validate password strength.
    Returns validation result with details.
    """
    result = {
        'valid': True,
        'errors': [],
        'score': 0,
        'strength': 'weak'
    }
    
    # Minimum length check
    if len(password) < 8:
        result['valid'] = False
        result['errors'].append('Password must be at least 8 characters long')
    else:
        result['score'] += 1
    
    # Maximum length check
    if len(password) > 128:
        result['valid'] = False
        result['errors'].append('Password must be less than 128 characters long')
    
    # Character type checks
    has_lower = bool(re.search(r'[a-z]', password))
    has_upper = bool(re.search(r'[A-Z]', password))
    has_digit = bool(re.search(r'\d', password))
    has_special = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
    
    if has_lower:
        result['score'] += 1
    else:
        result['errors'].append('Password must contain at least one lowercase letter')
    
    if has_upper:
        result['score'] += 1
    else:
        result['errors'].append('Password must contain at least one uppercase letter')
    
    if has_digit:
        result['score'] += 1
    else:
        result['errors'].append('Password must contain at least one digit')
    
    if has_special:
        result['score'] += 1
    else:
        result['errors'].append('Password must contain at least one special character')
    
    # Common password patterns
    common_patterns = [
        r'123456',
        r'password',
        r'qwerty',
        r'abc123',
        r'admin',
        r'letmein',
        r'welcome',
    ]
    
    for pattern in common_patterns:
        if re.search(pattern, password.lower()):
            result['valid'] = False
            result['errors'].append('Password contains common patterns')
            break
    
    # Sequential characters
    if re.search(r'(012|123|234|345|456|567|678|789|890)', password):
        result['score'] -= 1
        result['errors'].append('Password contains sequential numbers')
    
    if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', password.lower()):
        result['score'] -= 1
        result['errors'].append('Password contains sequential letters')
    
    # Repeated characters
    if re.search(r'(.)\1{2,}', password):
        result['score'] -= 1
        result['errors'].append('Password contains repeated characters')
    
    # Determine strength
    if result['score'] >= 4:
        result['strength'] = 'strong'
    elif result['score'] >= 3:
        result['strength'] = 'medium'
    else:
        result['strength'] = 'weak'
    
    # Final validation
    if len(result['errors']) > 0:
        result['valid'] = False
    
    return result


def validate_username(username: str) -> Dict[str, Any]:
    """Validate username format and constraints."""
    result = {
        'valid': True,
        'errors': []
    }
    
    # Length check
    if len(username) < 3:
        result['valid'] = False
        result['errors'].append('Username must be at least 3 characters long')
    
    if len(username) > 50:
        result['valid'] = False
        result['errors'].append('Username must be less than 50 characters long')
    
    # Character check
    if not re.match(r'^[a-zA-Z0-9_.-]+$', username):
        result['valid'] = False
        result['errors'].append('Username can only contain letters, numbers, dots, hyphens, and underscores')
    
    # Start/end character check
    if username.startswith('.') or username.endswith('.'):
        result['valid'] = False
        result['errors'].append('Username cannot start or end with a dot')
    
    if username.startswith('-') or username.endswith('-'):
        result['valid'] = False
        result['errors'].append('Username cannot start or end with a hyphen')
    
    # Reserved usernames
    reserved_usernames = [
        'admin', 'administrator', 'root', 'system', 'api', 'www', 'mail',
        'ftp', 'ssh', 'test', 'guest', 'anonymous', 'null', 'undefined'
    ]
    
    if username.lower() in reserved_usernames:
        result['valid'] = False
        result['errors'].append('Username is reserved')
    
    return result


def validate_filename(filename: str) -> Dict[str, Any]:
    """Validate filename for security and compatibility."""
    result = {
        'valid': True,
        'errors': []
    }
    
    # Length check
    if len(filename) > 255:
        result['valid'] = False
        result['errors'].append('Filename is too long (max 255 characters)')
    
    if len(filename) == 0:
        result['valid'] = False
        result['errors'].append('Filename cannot be empty')
    
    # Dangerous characters
    dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0']
    for char in dangerous_chars:
        if char in filename:
            result['valid'] = False
            result['errors'].append(f'Filename contains dangerous character: {char}')
    
    # Path traversal check
    if '..' in filename or filename.startswith('/') or filename.startswith('\\'):
        result['valid'] = False
        result['errors'].append('Filename contains path traversal patterns')
    
    # Reserved names (Windows)
    reserved_names = [
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5',
        'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4',
        'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]
    
    name_without_ext = filename.split('.')[0].upper()
    if name_without_ext in reserved_names:
        result['valid'] = False
        result['errors'].append('Filename uses reserved system name')
    
    return result


def sanitize_input(text: str, max_length: Optional[int] = None) -> str:
    """Sanitize user input by removing/escaping dangerous content."""
    if not text:
        return ""
    
    # Normalize unicode
    text = unicodedata.normalize('NFKC', text)
    
    # Remove null bytes
    text = text.replace('\0', '')
    
    # HTML escape
    text = html.escape(text)
    
    # Trim whitespace
    text = text.strip()
    
    # Truncate if needed
    if max_length and len(text) > max_length:
        text = text[:max_length]
    
    return text


def validate_tenant_id(tenant_id: str) -> bool:
    """Validate tenant ID format."""
    if not tenant_id:
        return False
    
    # Length check
    if len(tenant_id) < 1 or len(tenant_id) > 100:
        return False
    
    # Character check
    if not re.match(r'^[a-zA-Z0-9_-]+$', tenant_id):
        return False
    
    return True


def validate_department(department: str) -> bool:
    """Validate department name format."""
    if not department:
        return False
    
    # Length check
    if len(department) < 1 or len(department) > 100:
        return False
    
    # Character check (allow spaces and common punctuation)
    if not re.match(r'^[a-zA-Z0-9\s_.-]+$', department):
        return False
    
    return True


def validate_search_query(query: str) -> Dict[str, Any]:
    """Validate search query."""
    result = {
        'valid': True,
        'errors': [],
        'sanitized_query': ''
    }
    
    if not query:
        result['valid'] = False
        result['errors'].append('Search query cannot be empty')
        return result
    
    # Length check
    if len(query) > 1000:
        result['valid'] = False
        result['errors'].append('Search query is too long (max 1000 characters)')
    
    # Sanitize query
    sanitized = sanitize_input(query, max_length=1000)
    result['sanitized_query'] = sanitized
    
    # Check for potential injection attempts
    suspicious_patterns = [
        r'<script',
        r'javascript:',
        r'vbscript:',
        r'onload=',
        r'onerror=',
        r'eval\(',
        r'exec\(',
    ]
    
    for pattern in suspicious_patterns:
        if re.search(pattern, query.lower()):
            result['valid'] = False
            result['errors'].append('Search query contains suspicious content')
            break
    
    return result


def validate_json_data(data: Any, max_depth: int = 10, max_keys: int = 100) -> Dict[str, Any]:
    """Validate JSON data for safety."""
    result = {
        'valid': True,
        'errors': []
    }
    
    def check_depth(obj, current_depth=0):
        if current_depth > max_depth:
            result['valid'] = False
            result['errors'].append(f'JSON data exceeds maximum depth of {max_depth}')
            return
        
        if isinstance(obj, dict):
            if len(obj) > max_keys:
                result['valid'] = False
                result['errors'].append(f'JSON object exceeds maximum keys of {max_keys}')
                return
            
            for value in obj.values():
                check_depth(value, current_depth + 1)
        
        elif isinstance(obj, list):
            if len(obj) > max_keys:
                result['valid'] = False
                result['errors'].append(f'JSON array exceeds maximum length of {max_keys}')
                return
            
            for item in obj:
                check_depth(item, current_depth + 1)
    
    try:
        check_depth(data)
    except Exception as e:
        result['valid'] = False
        result['errors'].append(f'JSON validation error: {str(e)}')
    
    return result
