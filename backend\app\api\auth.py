"""
Authentication API endpoints for IntelliVault.
Handles user login, registration, token management, and password operations.
"""

from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON><PERSON><PERSON>F<PERSON>
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from loguru import logger

from ..database import get_db
from ..models.user import User, UserR<PERSON>, UserSession
from ..models.audit import AuditLog, AuditAction
from ..utils.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token,
    get_current_user,
    get_current_active_user,
    generate_reset_token,
    generate_verification_token,
    create_user_session,
    invalidate_user_session,
    rate_limiter,
)
from ..utils.validators import validate_email, validate_password, validate_username
from ..config import settings


router = APIRouter()


# Pydantic models for request/response
class UserCreate(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    password: str
    department: Optional[str] = None
    tenant_id: str = "default"


class UserLogin(BaseModel):
    username: str
    password: str


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenRefresh(BaseModel):
    refresh_token: str


class PasswordReset(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


class PasswordChange(BaseModel):
    current_password: str
    new_password: str


class UserResponse(BaseModel):
    id: str
    username: str
    email: str
    full_name: str
    role: str
    tenant_id: str
    department: Optional[str]
    is_active: bool
    is_verified: bool
    created_at: str
    last_login: Optional[str]


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """Register a new user."""
    
    # Rate limiting
    client_ip = request.client.host if request.client else "unknown"
    if not rate_limiter.is_allowed(f"register:{client_ip}"):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many registration attempts"
        )
    
    # Validate input
    if not validate_email(user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid email format"
        )
    
    username_validation = validate_username(user_data.username)
    if not username_validation['valid']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid username: {', '.join(username_validation['errors'])}"
        )
    
    password_validation = validate_password(user_data.password)
    if not password_validation['valid']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid password: {', '.join(password_validation['errors'])}"
        )
    
    # Check if user already exists
    existing_user = db.query(User).filter(
        (User.username == user_data.username) | (User.email == user_data.email)
    ).first()
    
    if existing_user:
        # Log failed registration attempt
        audit_log = AuditLog.create_log(
            action=AuditAction.USER_CREATED,
            description=f"Failed registration attempt - user already exists",
            success=False,
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent"),
            tenant_id=user_data.tenant_id,
        )
        db.add(audit_log)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    verification_token = generate_verification_token()
    
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        tenant_id=user_data.tenant_id,
        department=user_data.department,
        role=UserRole.USER,  # Default role
        verification_token=verification_token,
        verification_token_expires=datetime.now(timezone.utc) + timedelta(hours=24),
    )
    
    try:
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # Log successful registration
        audit_log = AuditLog.create_log(
            action=AuditAction.USER_CREATED,
            user_id=str(new_user.id),
            username=new_user.username,
            description=f"User registered successfully",
            success=True,
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent"),
            tenant_id=new_user.tenant_id,
        )
        db.add(audit_log)
        db.commit()
        
        logger.info(f"New user registered: {new_user.username} ({new_user.email})")
        
        # TODO: Send verification email
        
        return UserResponse(**new_user.to_dict())
        
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.post("/login", response_model=Token)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request = None,
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token."""
    
    # Rate limiting
    client_ip = request.client.host if request.client else "unknown"
    if not rate_limiter.is_allowed(f"login:{client_ip}"):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many login attempts"
        )
    
    # Find user
    user = db.query(User).filter(User.username == form_data.username).first()
    
    if not user or not verify_password(form_data.password, user.hashed_password):
        # Log failed login attempt
        audit_log = AuditLog.create_log(
            action=AuditAction.LOGIN_FAILED,
            username=form_data.username,
            description=f"Failed login attempt",
            success=False,
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent") if request else None,
        )
        db.add(audit_log)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    # Create tokens
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": str(user.id), "tenant_id": user.tenant_id},
        expires_delta=access_token_expires
    )
    
    refresh_token = create_refresh_token(
        data={"sub": user.username, "user_id": str(user.id)}
    )
    
    # Create user session
    session = await create_user_session(
        user=user,
        access_token=access_token,
        refresh_token=refresh_token,
        ip_address=client_ip,
        user_agent=request.headers.get("user-agent") if request else None,
        db=db
    )
    
    # Update last login
    user.last_login = datetime.now(timezone.utc)
    db.commit()
    
    # Log successful login
    audit_log = AuditLog.create_log(
        action=AuditAction.LOGIN,
        user_id=str(user.id),
        username=user.username,
        session_id=str(session.id),
        description=f"User logged in successfully",
        success=True,
        ip_address=client_ip,
        user_agent=request.headers.get("user-agent") if request else None,
        tenant_id=user.tenant_id,
    )
    db.add(audit_log)
    db.commit()
    
    logger.info(f"User logged in: {user.username}")
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.access_token_expire_minutes * 60
    )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token."""
    
    # Verify refresh token
    payload = verify_token(token_data.refresh_token)
    if not payload or payload.get("type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    username = payload.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Find user
    user = db.query(User).filter(User.username == username).first()
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new access token
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": str(user.id), "tenant_id": user.tenant_id},
        expires_delta=access_token_expires
    )
    
    return Token(
        access_token=access_token,
        refresh_token=token_data.refresh_token,  # Keep same refresh token
        expires_in=settings.access_token_expire_minutes * 60
    )


@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Logout user and invalidate session."""
    
    # Get token from request
    authorization = request.headers.get("Authorization")
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        
        # Invalidate session
        await invalidate_user_session(token, db)
    
    # Log logout
    audit_log = AuditLog.create_log(
        action=AuditAction.LOGOUT,
        user_id=str(current_user.id),
        username=current_user.username,
        description=f"User logged out",
        success=True,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        tenant_id=current_user.tenant_id,
    )
    db.add(audit_log)
    db.commit()
    
    logger.info(f"User logged out: {current_user.username}")
    
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return UserResponse(**current_user.to_dict())


@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Change user password."""
    
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )
    
    # Validate new password
    password_validation = validate_password(password_data.new_password)
    if not password_validation['valid']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid password: {', '.join(password_validation['errors'])}"
        )
    
    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()
    
    # Log password change
    audit_log = AuditLog.create_log(
        action=AuditAction.PASSWORD_CHANGED,
        user_id=str(current_user.id),
        username=current_user.username,
        description=f"Password changed successfully",
        success=True,
        tenant_id=current_user.tenant_id,
    )
    db.add(audit_log)
    db.commit()
    
    logger.info(f"Password changed for user: {current_user.username}")
    
    return {"message": "Password changed successfully"}


@router.post("/reset-password")
async def reset_password(
    reset_data: PasswordReset,
    request: Request,
    db: Session = Depends(get_db)
):
    """Request password reset."""
    
    # Rate limiting
    client_ip = request.client.host if request.client else "unknown"
    if not rate_limiter.is_allowed(f"reset:{client_ip}"):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many reset attempts"
        )
    
    # Find user
    user = db.query(User).filter(User.email == reset_data.email).first()
    
    if user:
        # Generate reset token
        reset_token = generate_reset_token()
        user.reset_token = reset_token
        user.reset_token_expires = datetime.now(timezone.utc) + timedelta(hours=1)
        db.commit()
        
        # TODO: Send reset email
        
        # Log password reset request
        audit_log = AuditLog.create_log(
            action=AuditAction.PASSWORD_RESET,
            user_id=str(user.id),
            username=user.username,
            description=f"Password reset requested",
            success=True,
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent"),
            tenant_id=user.tenant_id,
        )
        db.add(audit_log)
        db.commit()
        
        logger.info(f"Password reset requested for user: {user.email}")
    
    # Always return success to prevent email enumeration
    return {"message": "If the email exists, a reset link has been sent"}


@router.post("/reset-password/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """Confirm password reset with token."""
    
    # Find user with reset token
    user = db.query(User).filter(
        User.reset_token == reset_data.token,
        User.reset_token_expires > datetime.now(timezone.utc)
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    # Validate new password
    password_validation = validate_password(reset_data.new_password)
    if not password_validation['valid']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid password: {', '.join(password_validation['errors'])}"
        )
    
    # Update password and clear reset token
    user.hashed_password = get_password_hash(reset_data.new_password)
    user.reset_token = None
    user.reset_token_expires = None
    db.commit()
    
    # Log password reset completion
    audit_log = AuditLog.create_log(
        action=AuditAction.PASSWORD_RESET,
        user_id=str(user.id),
        username=user.username,
        description=f"Password reset completed",
        success=True,
        tenant_id=user.tenant_id,
    )
    db.add(audit_log)
    db.commit()
    
    logger.info(f"Password reset completed for user: {user.username}")
    
    return {"message": "Password reset successfully"}
