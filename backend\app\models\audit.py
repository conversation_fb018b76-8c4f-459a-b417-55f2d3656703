"""
Audit log model for tracking user actions and system events.
Provides comprehensive audit trail for compliance and security.
"""

import enum
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from uuid import uuid4

from sqlalchemy import Column, String, DateTime, Enum, Text, ForeignKey, JSO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..database import Base


class AuditAction(enum.Enum):
    """Audit action enumeration."""
    # Authentication actions
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    PASSWORD_CHANGED = "password_changed"
    PASSWORD_RESET = "password_reset"
    
    # User management actions
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    USER_ACTIVATED = "user_activated"
    USER_DEACTIVATED = "user_deactivated"
    ROLE_CHANGED = "role_changed"
    
    # Document actions
    DOCUMENT_UPLOADED = "document_uploaded"
    DOCUMENT_VIEWED = "document_viewed"
    DOCUMENT_DOWNLOADED = "document_downloaded"
    DOCUMENT_UPDATED = "document_updated"
    DOCUMENT_DELETED = "document_deleted"
    DOCUMENT_SHARED = "document_shared"
    DOCUMENT_PROCESSED = "document_processed"
    
    # Search actions
    SEARCH_PERFORMED = "search_performed"
    SEARCH_RESULT_CLICKED = "search_result_clicked"
    
    # System actions
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    SYSTEM_MAINTENANCE = "system_maintenance"
    CONFIGURATION_CHANGED = "configuration_changed"
    
    # Security actions
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    PERMISSION_DENIED = "permission_denied"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    
    # API actions
    API_CALL = "api_call"
    API_ERROR = "api_error"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"


class AuditLog(Base):
    """Audit log model for tracking all system activities."""
    
    __tablename__ = "audit_logs"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Action information
    action = Column(Enum(AuditAction), nullable=False, index=True)
    resource_type = Column(String(50), nullable=True, index=True)  # user, document, system, etc.
    resource_id = Column(String(100), nullable=True, index=True)
    
    # User information
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True)
    username = Column(String(50), nullable=True)  # Stored for deleted users
    
    # Session information
    session_id = Column(String(100), nullable=True, index=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    
    # Request information
    request_method = Column(String(10), nullable=True)
    request_path = Column(String(500), nullable=True)
    request_params = Column(JSON, nullable=True)
    
    # Response information
    response_status = Column(String(10), nullable=True)
    response_time = Column(Float, nullable=True)  # Response time in milliseconds
    
    # Event details
    description = Column(Text, nullable=True)
    details = Column(JSON, nullable=True)  # Additional event details
    
    # Result information
    success = Column(Boolean, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Multi-tenancy
    tenant_id = Column(String(100), nullable=True, index=True)
    
    # Timestamps
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")
    
    def __repr__(self) -> str:
        return f"<AuditLog(id={self.id}, action={self.action.value}, user_id={self.user_id})>"
    
    def to_dict(self) -> dict:
        """Convert audit log to dictionary representation."""
        return {
            "id": str(self.id),
            "action": self.action.value,
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "user_id": str(self.user_id) if self.user_id else None,
            "username": self.username,
            "session_id": self.session_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "request_method": self.request_method,
            "request_path": self.request_path,
            "request_params": self.request_params,
            "response_status": self.response_status,
            "response_time": self.response_time,
            "description": self.description,
            "details": self.details,
            "success": self.success,
            "error_message": self.error_message,
            "tenant_id": self.tenant_id,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }
    
    @classmethod
    def create_log(
        cls,
        action: AuditAction,
        user_id: Optional[str] = None,
        username: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        description: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: Optional[bool] = None,
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        request_method: Optional[str] = None,
        request_path: Optional[str] = None,
        request_params: Optional[Dict[str, Any]] = None,
        response_status: Optional[str] = None,
        response_time: Optional[float] = None,
    ) -> "AuditLog":
        """Create a new audit log entry."""
        return cls(
            action=action,
            user_id=user_id,
            username=username,
            resource_type=resource_type,
            resource_id=resource_id,
            description=description,
            details=details,
            success=success,
            error_message=error_message,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            tenant_id=tenant_id,
            request_method=request_method,
            request_path=request_path,
            request_params=request_params,
            response_status=response_status,
            response_time=response_time,
        )
    
    def is_security_event(self) -> bool:
        """Check if this is a security-related event."""
        security_actions = [
            AuditAction.LOGIN_FAILED,
            AuditAction.UNAUTHORIZED_ACCESS,
            AuditAction.PERMISSION_DENIED,
            AuditAction.SUSPICIOUS_ACTIVITY,
            AuditAction.RATE_LIMIT_EXCEEDED,
        ]
        return self.action in security_actions
    
    def is_user_action(self) -> bool:
        """Check if this is a user-initiated action."""
        return self.user_id is not None
    
    def is_system_action(self) -> bool:
        """Check if this is a system-initiated action."""
        system_actions = [
            AuditAction.SYSTEM_BACKUP,
            AuditAction.SYSTEM_RESTORE,
            AuditAction.SYSTEM_MAINTENANCE,
            AuditAction.DOCUMENT_PROCESSED,
        ]
        return self.action in system_actions


class AuditLogSummary(Base):
    """Summary statistics for audit logs."""
    
    __tablename__ = "audit_log_summaries"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Summary period
    period_start = Column(DateTime(timezone=True), nullable=False, index=True)
    period_end = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # hourly, daily, weekly, monthly
    
    # Tenant information
    tenant_id = Column(String(100), nullable=True, index=True)
    
    # Summary statistics
    total_events = Column(Integer, default=0, nullable=False)
    successful_events = Column(Integer, default=0, nullable=False)
    failed_events = Column(Integer, default=0, nullable=False)
    security_events = Column(Integer, default=0, nullable=False)
    
    # Action breakdown (stored as JSON)
    action_counts = Column(JSON, nullable=True)
    user_counts = Column(JSON, nullable=True)
    resource_counts = Column(JSON, nullable=True)
    
    # Performance metrics
    avg_response_time = Column(Float, nullable=True)
    max_response_time = Column(Float, nullable=True)
    min_response_time = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self) -> str:
        return f"<AuditLogSummary(id={self.id}, period={self.period_type}, events={self.total_events})>"
    
    def to_dict(self) -> dict:
        """Convert summary to dictionary representation."""
        return {
            "id": str(self.id),
            "period_start": self.period_start.isoformat() if self.period_start else None,
            "period_end": self.period_end.isoformat() if self.period_end else None,
            "period_type": self.period_type,
            "tenant_id": self.tenant_id,
            "total_events": self.total_events,
            "successful_events": self.successful_events,
            "failed_events": self.failed_events,
            "security_events": self.security_events,
            "action_counts": self.action_counts,
            "user_counts": self.user_counts,
            "resource_counts": self.resource_counts,
            "avg_response_time": self.avg_response_time,
            "max_response_time": self.max_response_time,
            "min_response_time": self.min_response_time,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
