"""
Configuration management for IntelliVault application.
Handles environment variables and application settings.
"""

import os
from typing import Optional, List
from pydantic import BaseSettings, validator
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application
    app_name: str = "IntelliVault"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # Database
    database_url: str = "postgresql://intellivault:password@localhost:5432/intellivault"
    database_echo: bool = False
    
    # Vector Database
    vector_db_path: str = "./data/chroma"
    vector_db_collection: str = "intellivault_embeddings"
    
    # Authentication
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # File Upload
    upload_path: str = "./uploads"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_extensions: List[str] = [
        ".pdf", ".docx", ".doc", ".pptx", ".ppt", ".xlsx", ".xls",
        ".png", ".jpg", ".jpeg", ".tiff", ".bmp", ".gif",
        ".mp4", ".avi", ".mov", ".mkv", ".wmv",
        ".mp3", ".wav", ".m4a", ".flac", ".aac"
    ]
    
    # AI Models
    openai_api_key: Optional[str] = None
    huggingface_api_key: Optional[str] = None
    google_vision_api_key: Optional[str] = None
    
    # Embedding Models
    text_embedding_model: str = "all-MiniLM-L6-v2"
    image_embedding_model: str = "openai/clip-vit-base-patch32"
    
    # OCR Settings
    tesseract_cmd: Optional[str] = None
    ocr_languages: List[str] = ["eng"]
    
    # Whisper Settings
    whisper_model: str = "base"
    whisper_device: str = "cpu"
    
    # Search Settings
    default_search_limit: int = 10
    max_search_limit: int = 100
    similarity_threshold: float = 0.7
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    log_file: Optional[str] = None
    
    # CORS
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    cors_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    cors_headers: List[str] = ["*"]
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    
    # Multi-tenancy
    enable_multi_tenancy: bool = True
    default_tenant: str = "default"
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # Redis (for caching and sessions)
    redis_url: Optional[str] = "redis://localhost:6379/0"
    cache_ttl: int = 3600  # 1 hour
    
    @validator("database_url", pre=True)
    def validate_database_url(cls, v):
        """Validate database URL format."""
        if not v.startswith(("postgresql://", "sqlite:///")):
            raise ValueError("Database URL must start with postgresql:// or sqlite:///")
        return v
    
    @validator("upload_path", pre=True)
    def create_upload_directory(cls, v):
        """Ensure upload directory exists."""
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator("vector_db_path", pre=True)
    def create_vector_db_directory(cls, v):
        """Ensure vector database directory exists."""
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator("allowed_extensions", pre=True)
    def normalize_extensions(cls, v):
        """Normalize file extensions to lowercase."""
        if isinstance(v, list):
            return [ext.lower() if ext.startswith('.') else f'.{ext.lower()}' for ext in v]
        return v
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


class DevelopmentSettings(Settings):
    """Development environment settings."""
    debug: bool = True
    reload: bool = True
    database_echo: bool = True
    log_level: str = "DEBUG"
    environment: str = "development"


class ProductionSettings(Settings):
    """Production environment settings."""
    debug: bool = False
    reload: bool = False
    database_echo: bool = False
    log_level: str = "INFO"
    environment: str = "production"
    
    @validator("secret_key")
    def validate_secret_key(cls, v):
        """Ensure secret key is changed in production."""
        if v == "your-secret-key-change-in-production":
            raise ValueError("Secret key must be changed in production")
        return v


class TestSettings(Settings):
    """Test environment settings."""
    debug: bool = True
    database_url: str = "sqlite:///./test.db"
    vector_db_path: str = "./test_data/chroma"
    upload_path: str = "./test_uploads"
    environment: str = "test"


@lru_cache()
def get_settings() -> Settings:
    """Get application settings based on environment."""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "test":
        return TestSettings()
    else:
        return DevelopmentSettings()


# Global settings instance
settings = get_settings()
