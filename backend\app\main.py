"""
Main FastAPI application for IntelliVault.
Entry point for the corporate knowledge mining platform.
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger
import uvicorn

from .config import settings
from .database import init_database, close_database
from .utils.logging_config import setup_logging
from .utils.security import get_current_user
from .models.audit import AuditLog, AuditAction

# Import API routers
from .api import auth, documents, search, admin, analytics


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting IntelliVault application...")
    
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized successfully")
        
        # Initialize vector database
        from .services.vector_service import VectorService
        vector_service = VectorService()
        await vector_service.initialize()
        logger.info("Vector database initialized successfully")
        
        # Initialize AI models
        from .services.embedding_service import EmbeddingService
        embedding_service = EmbeddingService()
        await embedding_service.initialize()
        logger.info("AI models initialized successfully")
        
        logger.info("IntelliVault application started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down IntelliVault application...")
    
    try:
        # Close database connections
        await close_database()
        logger.info("Database connections closed")
        
        # Cleanup vector database
        if 'vector_service' in locals():
            await vector_service.close()
            logger.info("Vector database closed")
        
        logger.info("IntelliVault application shut down successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Corporate Knowledge Mining Platform with Multimodal RAG",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
    lifespan=lifespan,
)

# Setup logging
setup_logging()

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

if settings.environment == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configure with actual allowed hosts in production
    )


# Request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time header to responses."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Audit logging middleware
@app.middleware("http")
async def audit_logging_middleware(request: Request, call_next):
    """Log API requests for audit purposes."""
    start_time = time.time()
    
    # Get user information if available
    user_id = None
    username = None
    try:
        # Try to get current user from token
        from .utils.security import get_user_from_token
        user = await get_user_from_token(request)
        if user:
            user_id = str(user.id)
            username = user.username
    except:
        pass  # No user authentication required for this request
    
    # Process request
    response = await call_next(request)
    
    # Calculate response time
    response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
    
    # Log API call (async, don't wait)
    asyncio.create_task(
        log_api_call(
            request=request,
            response=response,
            user_id=user_id,
            username=username,
            response_time=response_time,
        )
    )
    
    return response


async def log_api_call(
    request: Request,
    response,
    user_id: str = None,
    username: str = None,
    response_time: float = None,
):
    """Log API call to audit log."""
    try:
        from .database import get_async_session
        
        # Skip logging for health checks and static files
        if request.url.path in ["/health", "/metrics"] or request.url.path.startswith("/static"):
            return
        
        async with get_async_session() as session:
            audit_log = AuditLog.create_log(
                action=AuditAction.API_CALL,
                user_id=user_id,
                username=username,
                request_method=request.method,
                request_path=str(request.url.path),
                request_params=dict(request.query_params) if request.query_params else None,
                response_status=str(response.status_code),
                response_time=response_time,
                ip_address=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent"),
                success=200 <= response.status_code < 400,
            )
            
            session.add(audit_log)
            await session.commit()
            
    except Exception as e:
        logger.error(f"Failed to log API call: {e}")


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.status_code,
                "message": exc.detail,
                "type": "http_error",
            }
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    if settings.debug:
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": 500,
                    "message": str(exc),
                    "type": "internal_error",
                }
            },
        )
    else:
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": 500,
                    "message": "Internal server error",
                    "type": "internal_error",
                }
            },
        )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": settings.app_version,
        "environment": settings.environment,
        "timestamp": time.time(),
    }


# Metrics endpoint (for monitoring)
@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring."""
    if not settings.enable_metrics:
        raise HTTPException(status_code=404, detail="Metrics not enabled")
    
    # Basic metrics - can be extended with Prometheus metrics
    return {
        "app_info": {
            "name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
        },
        "system_info": {
            "timestamp": time.time(),
            "uptime": time.time(),  # This would need to be calculated properly
        },
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": "Corporate Knowledge Mining Platform with Multimodal RAG",
        "docs_url": "/docs" if settings.debug else None,
        "health_url": "/health",
        "api_prefix": "/api",
    }


# Include API routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(documents.router, prefix="/api/documents", tags=["Documents"])
app.include_router(search.router, prefix="/api/search", tags=["Search"])
app.include_router(admin.router, prefix="/api/admin", tags=["Administration"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["Analytics"])

# Mount static files (if serving frontend)
if settings.debug:
    try:
        app.mount("/static", StaticFiles(directory="frontend/static"), name="static")
    except RuntimeError:
        logger.warning("Static files directory not found, skipping mount")


# Development server
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower(),
        access_log=True,
    )
